{"version": 3, "names": ["_core", "require", "_traverse", "rewriteThisVisitor", "rewriteThis", "programPath", "visitors", "environmentVisitor", "ThisExpression", "path", "replaceWith", "t", "unaryExpression", "numericLiteral", "noScope", "traverse", "node"], "sources": ["../src/rewrite-this.ts"], "sourcesContent": ["import { types as t } from \"@babel/core\";\nimport traverse, { visitors, type NodePath } from \"@babel/traverse\";\n\n/**\n * A lazily constructed visitor to walk the tree, rewriting all `this` references in the\n * top-level scope to be `void 0` (undefined).\n *\n */\nlet rewriteThisVisitor: Parameters<typeof traverse>[1];\n\nexport default function rewriteThis(programPath: NodePath) {\n  if (!rewriteThisVisitor) {\n    rewriteThisVisitor = visitors.environmentVisitor({\n      ThisExpression(path) {\n        path.replaceWith(t.unaryExpression(\"void\", t.numericLiteral(0), true));\n      },\n    });\n    rewriteThisVisitor.noScope = true;\n  }\n  // Rewrite \"this\" to be \"undefined\".\n  traverse(programPath.node, rewriteThisVisitor);\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAD,OAAA;AAOA,IAAIE,kBAAkD;AAEvC,SAASC,WAAWA,CAACC,WAAqB,EAAE;EACzD,IAAI,CAACF,kBAAkB,EAAE;IACvBA,kBAAkB,GAAGG,kBAAQ,CAACC,kBAAkB,CAAC;MAC/CC,cAAcA,CAACC,IAAI,EAAE;QACnBA,IAAI,CAACC,WAAW,CAACC,WAAC,CAACC,eAAe,CAAC,MAAM,EAAED,WAAC,CAACE,cAAc,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;MACxE;IACF,CAAC,CAAC;IACFV,kBAAkB,CAACW,OAAO,GAAG,IAAI;EACnC;EAEA,IAAAC,iBAAQ,EAACV,WAAW,CAACW,IAAI,EAAEb,kBAAkB,CAAC;AAChD", "ignoreList": []}