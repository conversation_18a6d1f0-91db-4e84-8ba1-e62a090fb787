{"version": 3, "names": ["_get", "require", "_getPrototypeOf", "_superPropGet", "classArg", "property", "receiver", "flags", "result", "get", "getPrototypeOf", "prototype", "args", "apply"], "sources": ["../../src/helpers/superPropGet.ts"], "sourcesContent": ["/* @minVersion 7.25.0 */\n\nimport get from \"./get.ts\";\nimport getPrototypeOf from \"./getPrototypeOf.ts\";\n\nconst enum Flags {\n  Prototype = 0b1,\n  Call = 0b10,\n}\n\nexport default function _superPropGet(\n  classArg: any,\n  property: string,\n  receiver: any,\n  flags?: number,\n) {\n  var result = get(\n    getPrototypeOf(\n      // @ts-expect-error flags may be undefined\n      flags & Flags.Prototype ? classArg.prototype : classArg,\n    ),\n    property,\n    receiver,\n  );\n  // @ts-expect-error flags may be undefined\n  return flags & Flags.Call && typeof result === \"function\"\n    ? function (args: any[]) {\n        return result.apply(receiver, args);\n      }\n    : result;\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,IAAA,GAAAC,OAAA;AACA,IAAAC,eAAA,GAAAD,OAAA;AAOe,SAASE,aAAaA,CACnCC,QAAa,EACbC,QAAgB,EAChBC,QAAa,EACbC,KAAc,EACd;EACA,IAAIC,MAAM,GAAG,IAAAC,YAAG,EACd,IAAAC,uBAAc,EAEZH,KAAK,IAAkB,GAAGH,QAAQ,CAACO,SAAS,GAAGP,QACjD,CAAC,EACDC,QAAQ,EACRC,QACF,CAAC;EAED,OAAOC,KAAK,IAAa,IAAI,OAAOC,MAAM,KAAK,UAAU,GACrD,UAAUI,IAAW,EAAE;IACrB,OAAOJ,MAAM,CAACK,KAAK,CAACP,QAAQ,EAAEM,IAAI,CAAC;EACrC,CAAC,GACDJ,MAAM;AACZ", "ignoreList": []}